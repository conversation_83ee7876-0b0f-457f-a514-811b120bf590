"use client";
import NextLink from "next/link";
import { useState } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import {
  Navbar as HeroUINavbar,
  NavbarContent,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@heroui/navbar";
import { siteConfig } from "@/config/site";
import { useLanguage } from "@/contexts/LanguageContext";

export const Footer = () => {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const pathname = usePathname();

  const handleMenuItemClick = () => {
    setIsMenuOpen(false);
  };

  return (
    <HeroUINavbar
      isMenuOpen={isMenuOpen}
      maxWidth="xl"
      position="sticky"
      className={`bg-[#1d4930] backdrop-blur-md border-b-2 border-secondary shadow-sm ${isRTL ? "rtl" : "ltr"}`}
      classNames={{
        wrapper: "px-4 sm:px-6 lg:px-4",
      }}
      onMenuOpenChange={setIsMenuOpen}
    >
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink href="/" className="flex justify-start items-center gap-2">
            <div className="w-10 h-10 max-md:w-6 max-md:h-6 bg-white rounded-md flex items-center justify-center shadow-lg">
              <Image
                src="/pmw.png"
                alt="Proactive Wealth Management Logo"
                width={32}
                height={32}
                className="w-8 h-8 max-md:w-5 max-md:h-5"
                priority
              />
            </div>
            <div className="flex flex-col">
              <p className="font-semibold text-white text-sm max-md:text-xs leading-tight uppercase">
                Proactive
              </p>
              <p className="font-semibold text-white text-sm max-md:text-xs leading-tight mt-[2px] uppercase">
                Wealth Management
              </p>
            </div>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <ul
          className={`hidden lg:flex gap-8 justify-end ${isRTL ? "mr-auto" : "ml-auto"}`}
        >
          {siteConfig.navItems.map((item) => (
            <NavbarItem key={item.href}>
              <NextLink
                className={`text-white/90 hover:text-white font-medium text-sm transition-colors pb-2 border-b-2 ${
                  pathname === item.href
                    ? "border-secondary text-white"
                    : "border-transparent"
                }`}
                href={item.href}
              >
                {t(item.translationKey)}
              </NextLink>
            </NavbarItem>
          ))}
        </ul>
        <NavbarItem className="flex gap-4 items-center">
          <div className="flex gap-2 text-sm max-md:gap-1">
            <button
              className={`px-2 py-1 ${language === "EN" ? "text-white font-semibold" : "text-white/70"}`}
              onClick={() => setLanguage("EN")}
            >
              EN
            </button>
            <span className="text-white/50">|</span>
            <button
              className={`px-2 py-1 ${language === "AR" ? "text-white font-semibold" : "text-white/70"}`}
              onClick={() => setLanguage("AR")}
            >
              AR
            </button>
          </div>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent className="basis-1/5 sm:basis-full" justify="end">
        <button
          className="px-4 py-2 rounded-lg bg-gradient-to-r from-yellow-400 to-yellow-600 text-[#1d4930] font-bold shadow hover:from-yellow-500 hover:to-yellow-700 transition-colors border border-yellow-500"
          onClick={() => setShowModal(true)}
        >
          Book a Consultation
        </button>
      </NavbarContent>

      {/* Modal */}
      {showModal && (
        <>
          {/* Background Blur Overlay */}
          <div className="fixed inset-0 z-40 backdrop-blur-sm bg-black/30 transition-all duration-300"></div>

          {/* Modal Content */}
          <div className="fixed inset-0 z-50 flex justify-center items-center min-h-screen px-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md p-6 sm:p-8 relative flex flex-col items-center justify-center">
              <button
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-xl"
                onClick={() => {
                  setShowModal(false);
                  setIsSubmitted(false);
                }}
                aria-label="Close"
              >
                &times;
              </button>
              <h2 className="text-2xl font-bold text-[#1d4930] mb-6 text-center">
                Consultation booked successfully
              </h2>

              {isSubmitted ? (
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-[#1d4930] mb-4">
                    Thank you!
                  </h3>
                  <p className="text-sm text-gray-600">
                    We’ve received your consultation request.
                  </p>
                </div>
              ) : (
                <form
                  className="space-y-4 w-full"
                  onSubmit={(e) => {
                    e.preventDefault();
                    setIsSubmitted(true);
                  }}
                >
                  <div>
                    <label className="block text-sm font-medium text-[#1d4930] mb-1">
                      Name
                    </label>
                    <input
                      type="text"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-700"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#1d4930] mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-700"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#1d4930] mb-1">
                      Service
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-700">
                      <option>Strategic Tax Advisory</option>
                      <option>Seamless Company Formation</option>
                      <option>Secure Trust Establishment</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#1d4930] mb-1">
                      Goal
                    </label>
                    <input
                      type="text"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-700"
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full py-2 rounded-lg bg-gradient-to-r from-yellow-400 to-yellow-600 text-[#1d4930] font-bold shadow hover:from-yellow-500 hover:to-yellow-700 transition-colors border border-yellow-500 mt-4"
                  >
                    Book
                  </button>
                </form>
              )}
            </div>
          </div>
        </>
      )}

      <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
        <div className="flex gap-2 text-sm mr-2">
          <button
            className={`px-2 py-1 ${language === "EN" ? "text-white font-semibold" : "text-white/70"}`}
            onClick={() => setLanguage("EN")}
          >
            EN
          </button>
          <span className="text-white/50">|</span>
          <button
            className={`px-2 py-1 ${language === "AR" ? "text-white font-semibold" : "text-white/70"}`}
            onClick={() => setLanguage("AR")}
          >
            AR
          </button>
        </div>
        <NavbarMenuToggle
          className="text-white hover:text-white/80 transition-colors"
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          style={{
            color: "white",
            fontSize: "1.5rem",
            fontWeight: "bold",
          }}
        />
      </NavbarContent>
    </HeroUINavbar>
  );
};
