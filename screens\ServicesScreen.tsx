import React from "react";

const coreServices = [
  {
    title: "Strategic Tax Advisory",
    description:
      "Stay ahead with expert insights on UAE tax regulations, VAT compliance, and international tax planning to optimize financial outcomes.",
    items: [
      "Corporate tax planning",
      "VAT advisory",
      "International tax structuring",
      "Tax compliance",
    ],
    delay: "0.3s",
    anchor: "tax-advisory-anchor",
  },
  {
    title: "Seamless Company Formation",
    description:
      "We streamline your business setup in Dubai, handling legal structuring, licensing, and operational frameworks for a hassle-free launch.",
    items: [
      "Free zone companies",
      "Mainland companies",
      "Offshore companies",
      "Business licensing & support",
    ],
    delay: "0.4s",
    anchor: "company-formation-anchor",
  },
  {
    title: "Secure Trust Establishment",
    description:
      "Protect your assets and secure your wealth with robust trust solutions, ensuring long-term financial security and succession planning.",
    items: [
      "Private family trusts",
      "Asset protection trusts",
      "Business succession trusts",
      "Foundation establishment",
    ],
    delay: "0.5s",
    anchor: "trust-establishment-anchor",
  },
];

const workSteps = [
  {
    title: "1. Consultation & Strategy",
    description:
      "We begin with a deep-dive consultation to analyze your needs and develop a strategic plan tailored for success.",
    delay: "0.3s",
  },
  {
    title: "2. Implementation & Compliance",
    description:
      "Our dedicated experts handle all legal, financial, and administrative requirements with precision and care, ensuring full compliance.",
    delay: "0.4s",
  },
  {
    title: "3. Ongoing Support",
    description:
      "We remain your partner, providing continuous support to optimize and ensure your long-term success and growth.",
    delay: "0.5s",
  },
];

const ServicesScreen: React.FC = () => {
  return (
    <div className="bg-gradient-to-br from-slate-50 to-white min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#e9f5ec] to-white py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-[#1d4930]">
              Our Core <span className="text-green-700">Services</span>
            </h1>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              We provide end-to-end solutions across tax advisory, company formation, and trust establishment, creating seamless integration for your business.
            </p>
          </div>
        </div>
      </div>

      {/* Services Cards */}
      <div className="py-20 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-8 mb-16">
            {coreServices.map((service, idx) => (
              <div
                key={service.title}
                id={service.anchor}
                className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:border-green-700/40 hover:shadow-xl transition-all duration-300 anim-target anim-fade-up is-visible"
                style={{ transitionDelay: service.delay }}
              >
                <h3 className="text-xl font-bold text-[#1d4930] mb-4">{service.title}</h3>
                <p className="text-gray-600 mb-4">{service.description}</p>
                <ul className="list-disc list-inside text-gray-700 space-y-1 pl-2">
                  {service.items.map((item) => (
                    <li key={item}>{item}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* How We Work Section */}
          <div className="bg-gradient-to-br from-[#1d4930] to-[#2d5940] rounded-3xl p-10 mb-20 border border-green-700/30 shadow-2xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-white mb-4 tracking-tight drop-shadow">
                How We <span className="text-green-300">Work</span>
              </h2>
              <p className="text-gray-100 max-w-2xl mx-auto text-lg">
                Our proven process ensures clarity, efficiency, and results tailored to your unique objectives.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-10">
              {workSteps.map((step, idx) => (
                <div
                  key={step.title}
                  className="bg-white rounded-2xl p-8 shadow-xl border border-green-700/10 hover:shadow-2xl hover:border-green-700/40 transition-all duration-300 flex flex-col items-center anim-target anim-fade-up is-visible"
                  style={{ transitionDelay: step.delay }}
                >
                  <div className="mb-4">
                    <span className="inline-block bg-green-100 text-green-700 rounded-full p-3 shadow">
                      {idx === 0 && (
                        <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" className="inline">
                          <circle cx="14" cy="14" r="12" />
                          <path d="M14 8v8l6 4" />
                        </svg>
                      )}
                      {idx === 1 && (
                        <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" className="inline">
                          <rect x="6" y="6" width="16" height="16" rx="4" />
                          <path d="M10 14h8" />
                        </svg>
                      )}
                      {idx === 2 && (
                        <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" className="inline">
                          <path d="M14 4v20" />
                          <path d="M4 14h20" />
                        </svg>
                      )}
                    </span>
                  </div>
                  <h3 className="font-bold text-lg text-[#1d4930] mb-2 text-center">{step.title}</h3>
                  <p className="text-gray-600 text-center">{step.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServicesScreen;