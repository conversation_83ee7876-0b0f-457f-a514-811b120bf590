import React from "react";

const coreServices = [
  {
    title: "services.tax.title",
    descriptionKey: "services.tax.description",
    itemsKeys: [
      "services.tax.items.corporate",
      "services.tax.items.vat",
      "services.tax.items.international",
      "services.tax.items.compliance",
    ],
    delay: "0.3s",
    anchor: "tax-advisory-anchor",
  },
  {
    title: "services.company.title",
    descriptionKey: "services.company.description",
    itemsKeys: [
      "services.company.items.freezone",
      "services.company.items.mainland",
      "services.company.items.offshore",
      "services.company.items.licensing",
    ],
    delay: "0.4s",
    anchor: "company-formation-anchor",
  },
  {
    title: "services.trust.title",
    descriptionKey: "services.trust.description",
    itemsKeys: [
      "services.trust.items.family",
      "services.trust.items.asset",
      "services.trust.items.succession",
      "services.trust.items.foundation",
    ],
    delay: "0.5s",
    anchor: "trust-establishment-anchor",
  },
];

const workSteps = [
  {
    title: services.work.step1.title,
    descriptionKey: "services.work.step1.description",
    delay: "0.3s",
  },
  {
    title: "services.work.step2.title",
    descriptionKey: "services.work.step2.description",
    delay: "0.4s",
  },
  {
    title: "services.work.step3.title",
    descriptionKey: "services.work.step3.description",
    delay: "0.5s",
  },
];

const ServicesScreen: React.FC = () => {
  return (
    <div className="bg-gradient-to-br from-slate-50 to-white min-h-screen">
      {/* Hero Section */}
      <div className="relative h-[60vh] w-full">
        {/* Background image with blur */}
        <div className="absolute inset-0 bg-[url('/service-hero.png')] bg-cover bg-center backdrop-blur-sm z-0" />

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-[#e9f5ec]/90 via-white/80 to-white/60 z-0" />

        {/* Content */}
        <div className="relative h-[65vh] w-full overflow-hidden">
          {/* Background image with blur */}
          <div className="absolute inset-0 bg-[url('/service-hero.png')] bg-cover bg-center backdrop-blur-sm brightness-90 z-0" />

          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-[#e9f5ec]/90 via-white/80 to-white/95 z-0" />

          {/* Content */}
          <div className="relative z-10 flex items-center justify-center h-full px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold leading-tight mb-6 text-[#1d4930] drop-shadow-sm">
                Our Core <span className="text-green-700">Services</span>
              </h1>
              <p className="text-lg md:text-xl text-[#1d4930] max-w-3xl mx-auto font-medium">
                We provide end-to-end solutions across tax advisory, company
                formation, and trust establishment — creating seamless
                integration for your business from inception to growth.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Services Cards */}
      <div className="py-20 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-8 mb-16">
            {coreServices.map((service, idx) => (
              <div
                key={service.title}
                id={service.anchor}
                className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:border-green-700/40 hover:shadow-xl transition-all duration-300 anim-target anim-fade-up is-visible"
                style={{ transitionDelay: service.delay }}
              >
                <h3 className="text-xl font-bold text-[#1d4930] mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-4">{service.descriptionKey}</p>
                <ul className="space-y-2">
                  {service.itemsKeys.map((item, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      {/* Golden Tick Icon */}
                      <svg
                        className="w-5 h-5 flex-shrink-0 mt-1 text-[#eab308]"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="3"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-gray-700">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* How We Work Section */}
          <div className="bg-gradient-to-br from-[#1d4930] to-[#2d5940] rounded-3xl p-10 mb-20 border border-green-700/30 shadow-2xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-white mb-4 tracking-tight drop-shadow">
                How We <span className="text-green-300">Work</span>
              </h2>
              <p className="text-gray-100 max-w-2xl mx-auto text-lg">
                Our proven process ensures clarity, efficiency, and results
                tailored to your unique objectives.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-10">
              {workSteps.map((step, idx) => (
                <div
                  key={step.title}
                  className="bg-white rounded-2xl p-8 shadow-xl border border-green-700/10 hover:shadow-2xl hover:border-green-700/40 transition-all duration-300 flex flex-col items-center anim-target anim-fade-up is-visible"
                  style={{ transitionDelay: step.delay }}
                >
                  <div className="mb-4">
                    <span className="inline-block bg-green-100 text-green-700 rounded-full p-3 shadow">
                      {idx === 0 && (
                        <svg
                          width="28"
                          height="28"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="inline"
                        >
                          <circle cx="14" cy="14" r="12" />
                          <path d="M14 8v8l6 4" />
                        </svg>
                      )}
                      {idx === 1 && (
                        <svg
                          width="28"
                          height="28"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="inline"
                        >
                          <rect x="6" y="6" width="16" height="16" rx="4" />
                          <path d="M10 14h8" />
                        </svg>
                      )}
                      {idx === 2 && (
                        <svg
                          width="28"
                          height="28"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="inline"
                        >
                          <path d="M14 4v20" />
                          <path d="M4 14h20" />
                        </svg>
                      )}
                    </span>
                  </div>
                  <h3 className="font-bold text-lg text-[#1d4930] mb-2 text-center">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 text-center">
                    {step.descriptionKey}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServicesScreen;
