"use client";
import {
  Navbar as Hero<PERSON><PERSON>av<PERSON>,
  NavbarContent,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@heroui/navbar";
import NextLink from "next/link";
import { useState } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { siteConfig } from "@/config/site";
import { useLanguage } from "@/contexts/LanguageContext";

export const Navbar = () => {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const handleMenuItemClick = () => {
    setIsMenuOpen(false);
  };

  return (
    <HeroUINavbar
      isMenuOpen={isMenuOpen}
      maxWidth="xl"
      position="sticky"
      className={`bg-[#1d4930] backdrop-blur-md border-b-2 border-secondary shadow-sm ${isRTL ? "rtl" : "ltr"}`}
      classNames={{
        wrapper: "px-4 sm:px-6 lg:px-4",
      }}
      onMenuOpenChange={setIsMenuOpen}
    >
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink className="flex justify-start items-center gap-2" href="/">
            <div className="w-10 h-10 max-md:w-6 max-md:h-6 rounded-md flex items-center justify-center shadow-lg">
              <Image
                src="/pmw.png"
                alt="Proactive Wealth Management Logo"
                width={32}
                height={32}
                className="w-10 h-10 max-md:w-5 max-md:h-5  rounded-md"
                priority
              />
            </div>
            <div className="flex flex-col">
              <p className="font-semibold text-white text-sm max-md:text-xs leading-tight uppercase">
                Proactive
              </p>
              <p className="font-semibold text-white text-sm max-md:text-xs leading-tight mt-[2px] uppercase">
                Wealth Management
              </p>
            </div>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <ul
          className={`hidden lg:flex gap-8 justify-end ${isRTL ? "mr-auto" : "ml-auto"}`}
        >
          {siteConfig.navItems.map((item) => (
            <NavbarItem key={item.href}>
              <NextLink
                className={`text-white/90 hover:text-white font-medium text-sm transition-colors pb-2 border-b-2 ${
                  pathname === item.href
                    ? "border-secondary text-white"
                    : "border-transparent"
                }`}
                href={item.href}
              >
                {t(item.translationKey)}
              </NextLink>
            </NavbarItem>
          ))}
        </ul>
        <NavbarItem className="flex gap-4 items-center">
          <div className="flex gap-2 text-sm max-md:gap-1">
            <button
              className={`px-2 py-1 ${language === "EN" ? "text-white font-semibold" : "text-white/70"}`}
              onClick={() => setLanguage("EN")}
            >
              EN
            </button>
            <span className="text-white/50">|</span>
            <button
              className={`px-2 py-1 ${language === "AR" ? "text-white font-semibold" : "text-white/70"}`}
              onClick={() => setLanguage("AR")}
            >
              AR
            </button>
          </div>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
        <div className="flex gap-2 text-sm mr-2">
          <button
            className={`px-2 py-1 ${language === "EN" ? "text-white font-semibold" : "text-white/70"}`}
            onClick={() => setLanguage("EN")}
          >
            EN
          </button>
          <span className="text-white/50">|</span>
          <button
            className={`px-2 py-1 ${language === "AR" ? "text-white font-semibold" : "text-white/70"}`}
            onClick={() => setLanguage("AR")}
          >
            AR
          </button>
        </div>
        <NavbarMenuToggle
          className="text-white hover:text-white/80 transition-colors"
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          style={{
            color: "white",
            fontSize: "1.5rem",
            fontWeight: "bold",
          }}
        />
      </NavbarContent>

      <NavbarMenu className="bg-primary">
        <div
          className={`mx-4 mt-2 flex flex-col gap-2 ${isRTL ? "text-right" : "text-left"}`}
        >
          {siteConfig.navItems.map((item, index) => (
            <NavbarMenuItem key={`${item.label}-${index}`}>
              <NextLink
                className={`font-medium text-lg py-2 transition-colors ${
                  isRTL ? "border-r-4 pr-4" : "border-l-4 pl-4"
                } ${
                  pathname === item.href
                    ? "border-secondary text-white bg-white/10"
                    : "border-transparent text-white/90 hover:text-white"
                }`}
                href={item.href}
                onClick={handleMenuItemClick}
              >
                {t(item.translationKey)}
              </NextLink>
            </NavbarMenuItem>
          ))}
        </div>
      </NavbarMenu>
    </HeroUINavbar>
  );
};
